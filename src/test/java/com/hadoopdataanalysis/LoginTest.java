package com.hadoopdataanalysis;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * 登录功能测试类
 */
@SpringBootTest
@TestPropertySource(properties = {
    "hadoop.username=wangjp",
    "hadoop.password=qqqq1111",
    "hadoop.base-url=https://hadoop.greatld.com/dqp/dataAnalysis",
    "logging.level.com.hadoopdataanalysis=DEBUG"
})
public class LoginTest {

    @Autowired
    private DataAnalysisService dataAnalysisService;

    @Test
    public void testLogin() {
        try {
            System.out.println("=== 开始登录测试 ===");
            System.out.println("用户名: wangjp");
            System.out.println("基础URL: https://hadoop.greatld.com");
            System.out.println("==================");

            // 执行登录测试
            String result = dataAnalysisService.login();

            System.out.println("=== 登录测试结果 ===");
            System.out.println(result);
            System.out.println("==================");

            // 验证结果
            if (result.contains("success")) {
                System.out.println("✓ 登录成功！");
            } else if (result.contains("manual_required")) {
                System.out.println("⚠ 需要手动登录");
            } else {
                System.out.println("✗ 登录失败");
                System.out.println("建议：检查网络连接、用户名密码或网站登录机制变化");
            }

        } catch (Exception e) {
            System.err.println("登录测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testGetConnectionStatus() {
        try {
            // 测试连接状态
            String result = dataAnalysisService.getConnectionStatus();

            System.out.println("=== 连接状态测试结果 ===");
            System.out.println(result);
            System.out.println("=====================");

        } catch (Exception e) {
            System.err.println("连接状态测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testDirectApiLogin() {
        try {
            System.out.println("=== 直接API登录测试 ===");

            // 直接测试 /dmpapi/login 端点
            org.apache.hc.client5.http.impl.classic.CloseableHttpClient httpClient =
                org.apache.hc.client5.http.impl.classic.HttpClients.createDefault();

            org.apache.hc.client5.http.classic.methods.HttpPost loginRequest =
                new org.apache.hc.client5.http.classic.methods.HttpPost("https://hadoop.greatld.com/dmpapi/login");

            // 设置请求头
            loginRequest.setHeader("Content-Type", "application/json;charset=UTF-8");
            loginRequest.setHeader("Accept", "application/json, text/plain, */*");
            loginRequest.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36");
            loginRequest.setHeader("Referer", "https://hadoop.greatld.com/login");
            loginRequest.setHeader("Origin", "https://hadoop.greatld.com");

            // 准备登录数据
            String loginJson = "{\"username\":\"wangjp\",\"password\":\"qqqq1111\"}";
            loginRequest.setEntity(new org.apache.hc.core5.http.io.entity.StringEntity(loginJson,
                org.apache.hc.core5.http.ContentType.APPLICATION_JSON));

            try (org.apache.hc.client5.http.impl.classic.CloseableHttpResponse response = httpClient.execute(loginRequest)) {
                int statusCode = response.getCode();
                String responseBody = org.apache.hc.core5.http.io.entity.EntityUtils.toString(response.getEntity());

                System.out.println("状态码: " + statusCode);
                System.out.println("响应内容: " + responseBody);

                if (statusCode == 200) {
                    System.out.println("✓ API调用成功");
                } else {
                    System.out.println("✗ API调用失败");
                }
            }

            httpClient.close();

        } catch (Exception e) {
            System.err.println("直接API登录测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

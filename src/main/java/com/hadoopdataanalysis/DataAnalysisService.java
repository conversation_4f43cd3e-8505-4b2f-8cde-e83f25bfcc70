package com.hadoopdataanalysis;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.entity.UrlEncodedFormEntity;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.core5.http.NameValuePair;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.message.BasicNameValuePair;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Hadoop数据分析服务类
 * 
 * 通过Web页面方式访问Hadoop数据分析平台
 * 提供表查询、结构获取、SQL执行等功能
 */
@Service
public class DataAnalysisService {

    private static final Logger logger = LoggerFactory.getLogger(DataAnalysisService.class);

    @Value("${hadoop.base-url:https://hadoop.greatld.com}")
    private String baseUrl;

    @Value("${hadoop.username:}")
    private String username;

    @Value("${hadoop.password:}")
    private String password;

    @Value("${hadoop.analysis-path:/dqp/dataAnalysis}")
    private String analysisPath;

    private final ObjectMapper objectMapper;
    private CloseableHttpClient httpClient;
    private String sessionCookie;

    public DataAnalysisService(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
        this.httpClient = HttpClients.createDefault();
    }

    /**
     * 登录到Hadoop数据分析平台
     * 
     * @return 登录结果信息
     */
    @Tool(description = "登录到Hadoop数据分析平台，获取访问权限")
    public String login() {
        try {
            if (username == null || username.trim().isEmpty()) {
                return "错误：未配置用户名，请设置环境变量 HADOOP_USERNAME";
            }
            
            if (password == null || password.trim().isEmpty()) {
                return "错误：未配置密码，请设置环境变量 HADOOP_PASSWORD";
            }

            // 首先访问登录页面获取必要的参数
            HttpGet loginPageRequest = new HttpGet(baseUrl + "/login");
            
            try (CloseableHttpResponse response = httpClient.execute(loginPageRequest)) {
                String loginPageHtml = EntityUtils.toString(response.getEntity());
                Document loginDoc = Jsoup.parse(loginPageHtml);
                
                // 查找登录表单
                Element loginForm = loginDoc.select("form").first();
                if (loginForm == null) {
                    return "错误：无法找到登录表单";
                }
                
                // 准备登录参数
                List<NameValuePair> loginParams = new ArrayList<>();
                loginParams.add(new BasicNameValuePair("username", username));
                loginParams.add(new BasicNameValuePair("password", password));
                
                // 查找CSRF token等隐藏字段
                Elements hiddenInputs = loginForm.select("input[type=hidden]");
                for (Element input : hiddenInputs) {
                    String name = input.attr("name");
                    String value = input.attr("value");
                    if (!name.isEmpty()) {
                        loginParams.add(new BasicNameValuePair(name, value));
                    }
                }
                
                // 执行登录
                HttpPost loginRequest = new HttpPost(baseUrl + "/login");
                loginRequest.setEntity(new UrlEncodedFormEntity(loginParams));
                
                try (CloseableHttpResponse loginResponse = httpClient.execute(loginRequest)) {
                    // 获取会话Cookie
                    if (loginResponse.getHeaders("Set-Cookie").length > 0) {
                        sessionCookie = loginResponse.getHeaders("Set-Cookie")[0].getValue();
                    }
                    
                    int statusCode = loginResponse.getCode();
                    if (statusCode == 302 || statusCode == 200) {
                        Map<String, Object> result = new HashMap<>();
                        result.put("status", "success");
                        result.put("message", "登录成功");
                        result.put("username", username);
                        result.put("baseUrl", baseUrl);
                        
                        return objectMapper.writeValueAsString(result);
                    } else {
                        return "登录失败：HTTP状态码 " + statusCode;
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("登录失败", e);
            return "登录失败: " + e.getMessage();
        }
    }

    /**
     * 获取数据库列表
     * 
     * @return 数据库列表的JSON字符串
     */
    @Tool(description = "获取Hadoop数据分析平台中的所有数据库列表")
    public String getDatabaseList() {
        try {
            // 确保已登录
            if (sessionCookie == null) {
                String loginResult = login();
                if (!loginResult.contains("success")) {
                    return loginResult;
                }
            }
            
            // 访问数据分析页面
            HttpGet request = new HttpGet(baseUrl + analysisPath);
            if (sessionCookie != null) {
                request.setHeader("Cookie", sessionCookie);
            }
            
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                String html = EntityUtils.toString(response.getEntity());
                Document doc = Jsoup.parse(html);
                
                // 解析数据库列表
                List<Map<String, Object>> databases = new ArrayList<>();
                
                // 查找数据库树结构
                Elements dbElements = doc.select(".database-tree .database-item, .tree-node[data-type=database]");
                
                for (Element dbElement : dbElements) {
                    Map<String, Object> database = new HashMap<>();
                    
                    String dbName = dbElement.text();
                    if (dbName.contains(".")) {
                        dbName = dbName.substring(0, dbName.indexOf("."));
                    }
                    
                    database.put("databaseName", dbName);
                    database.put("type", "database");
                    
                    // 尝试获取表数量
                    Elements tableElements = dbElement.parent().select(".table-item");
                    database.put("tableCount", tableElements.size());
                    
                    databases.add(database);
                }
                
                // 如果没有找到特定的数据库元素，尝试从页面脚本中解析
                if (databases.isEmpty()) {
                    Elements scripts = doc.select("script");
                    for (Element script : scripts) {
                        String scriptContent = script.html();
                        if (scriptContent.contains("applydata") || scriptContent.contains("database")) {
                            // 尝试解析JavaScript中的数据库信息
                            if (scriptContent.contains("applydata")) {
                                databases.add(createDatabaseInfo("applydata"));
                            }
                            if (scriptContent.contains("applydata_bi")) {
                                databases.add(createDatabaseInfo("applydata_bi"));
                            }
                            if (scriptContent.contains("applydata_bi_ads")) {
                                databases.add(createDatabaseInfo("applydata_bi_ads"));
                            }
                            if (scriptContent.contains("applydata_bi_dw")) {
                                databases.add(createDatabaseInfo("applydata_bi_dw"));
                            }
                            break;
                        }
                    }
                }
                
                Map<String, Object> result = new HashMap<>();
                result.put("total", databases.size());
                result.put("databases", databases);
                result.put("source", "hadoop-data-analysis-platform");
                
                return objectMapper.writeValueAsString(result);
            }
            
        } catch (Exception e) {
            logger.error("获取数据库列表失败", e);
            return "获取数据库列表失败: " + e.getMessage();
        }
    }

    /**
     * 搜索数据库表
     * 
     * @param tableNamePattern 表名模式，支持库名.表名格式
     * @param schemaName 数据库名称
     * @param limit 返回结果数量限制
     * @return 表列表的JSON字符串
     */
    @Tool(description = "搜索Hadoop数据分析平台中的表，支持库名.表名格式搜索")
    public String searchTables(String tableNamePattern, String schemaName, int limit) {
        try {
            // 确保已登录
            if (sessionCookie == null) {
                String loginResult = login();
                if (!loginResult.contains("success")) {
                    return loginResult;
                }
            }
            
            if (limit <= 0) limit = 50;
            if (limit > 200) limit = 200;
            
            // 处理库名.表名格式
            String actualSchema = schemaName;
            String actualTablePattern = tableNamePattern;
            
            if (tableNamePattern != null && tableNamePattern.contains(".")) {
                String[] parts = tableNamePattern.split("\\.", 2);
                actualSchema = parts[0];
                actualTablePattern = parts[1];
            }
            
            // 访问数据分析页面
            HttpGet request = new HttpGet(baseUrl + analysisPath);
            if (sessionCookie != null) {
                request.setHeader("Cookie", sessionCookie);
            }
            
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                String html = EntityUtils.toString(response.getEntity());
                Document doc = Jsoup.parse(html);
                
                List<Map<String, Object>> tables = new ArrayList<>();
                
                // 查找表列表
                Elements tableElements = doc.select(".table-item, .tree-node[data-type=table]");
                
                int count = 0;
                for (Element tableElement : tableElements) {
                    if (count >= limit) break;
                    
                    String tableName = tableElement.text();
                    String fullTableName = tableName;
                    
                    // 如果表名不包含库名，添加库名
                    if (!tableName.contains(".") && actualSchema != null) {
                        fullTableName = actualSchema + "." + tableName;
                    }
                    
                    // 检查是否匹配搜索模式
                    if (actualTablePattern != null && !actualTablePattern.isEmpty()) {
                        if (!tableName.toLowerCase().contains(actualTablePattern.toLowerCase())) {
                            continue;
                        }
                    }
                    
                    Map<String, Object> table = new HashMap<>();
                    table.put("tableName", tableName);
                    table.put("fullName", fullTableName);
                    table.put("schema", actualSchema);
                    table.put("tableType", "TABLE");
                    
                    tables.add(table);
                    count++;
                }
                
                Map<String, Object> result = new HashMap<>();
                result.put("total", tables.size());
                result.put("tables", tables);
                result.put("searchPattern", tableNamePattern);
                result.put("schema", actualSchema);
                result.put("limit", limit);
                
                return objectMapper.writeValueAsString(result);
            }
            
        } catch (Exception e) {
            logger.error("搜索表失败", e);
            return "搜索表失败: " + e.getMessage();
        }
    }

    /**
     * 创建数据库信息对象
     */
    private Map<String, Object> createDatabaseInfo(String dbName) {
        Map<String, Object> database = new HashMap<>();
        database.put("databaseName", dbName);
        database.put("type", "database");
        database.put("tableCount", 0);
        return database;
    }

    /**
     * 获取表结构信息
     *
     * @param tableName 表名，支持库名.表名格式
     * @return 表结构信息的JSON字符串
     */
    @Tool(description = "获取指定表的结构信息，包括字段名称和类型")
    public String getTableStructure(String tableName) {
        try {
            // 确保已登录
            if (sessionCookie == null) {
                String loginResult = login();
                if (!loginResult.contains("success")) {
                    return loginResult;
                }
            }

            // 构造DESCRIBE查询
            String describeSQL = "DESCRIBE " + tableName;

            Map<String, Object> result = new HashMap<>();
            result.put("tableName", tableName);
            result.put("sql", describeSQL);
            result.put("message", "请使用executeSQL工具执行以下SQL查询来获取表结构");
            result.put("suggestedSQL", describeSQL);

            return objectMapper.writeValueAsString(result);

        } catch (Exception e) {
            logger.error("获取表结构失败", e);
            return "获取表结构失败: " + e.getMessage();
        }
    }

    /**
     * 生成快捷SQL语句
     *
     * @param tableName 表名
     * @param sqlType SQL类型：select、count、describe
     * @return 生成的SQL语句
     */
    @Tool(description = "为指定表生成快捷SQL语句，支持select、count、describe类型")
    public String generateQuickSQL(String tableName, String sqlType) {
        try {
            if (tableName == null || tableName.trim().isEmpty()) {
                return "表名不能为空";
            }

            String actualTableName = tableName.trim();

            String sql;
            switch (sqlType.toLowerCase()) {
                case "select":
                    sql = "SELECT * FROM " + actualTableName + " LIMIT 101";
                    break;
                case "count":
                    sql = "SELECT COUNT(1) FROM " + actualTableName;
                    break;
                case "describe":
                case "desc":
                    sql = "DESCRIBE " + actualTableName;
                    break;
                default:
                    return "不支持的SQL类型，支持的类型：select、count、describe";
            }

            Map<String, Object> result = new HashMap<>();
            result.put("tableName", actualTableName);
            result.put("sqlType", sqlType);
            result.put("sql", sql);
            result.put("message", "已生成SQL语句，请使用executeSQL工具执行");

            return objectMapper.writeValueAsString(result);

        } catch (Exception e) {
            logger.error("生成快捷SQL失败", e);
            return "生成快捷SQL失败: " + e.getMessage();
        }
    }

    /**
     * 执行SQL查询（模拟）
     *
     * @param sql SQL查询语句
     * @param maxRows 最大返回行数
     * @return 查询结果说明
     */
    @Tool(description = "模拟SQL查询执行，返回执行说明（实际执行需要在Web页面中进行）")
    public String executeSQL(String sql, int maxRows) {
        try {
            if (maxRows <= 0) maxRows = 101;
            if (maxRows > 10000) maxRows = 10000;

            // 安全检查
            String trimmedSql = sql.trim().toUpperCase();
            if (!trimmedSql.startsWith("SELECT") && !trimmedSql.startsWith("DESCRIBE") && !trimmedSql.startsWith("SHOW")) {
                return "安全限制：只允许执行SELECT、DESCRIBE、SHOW查询语句";
            }

            Map<String, Object> result = new HashMap<>();
            result.put("sql", sql);
            result.put("maxRows", maxRows);
            result.put("status", "prepared");
            result.put("message", "SQL语句已准备就绪");
            result.put("instructions", "请在Hadoop数据分析平台页面 (" + baseUrl + analysisPath + ") 中执行此SQL语句");
            result.put("webUrl", baseUrl + analysisPath);

            return objectMapper.writeValueAsString(result);

        } catch (Exception e) {
            logger.error("SQL执行准备失败", e);
            return "SQL执行准备失败: " + e.getMessage();
        }
    }

    /**
     * 获取平台连接状态
     *
     * @return 连接状态信息
     */
    @Tool(description = "获取Hadoop数据分析平台的连接状态和基本信息")
    public String getConnectionStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("baseUrl", baseUrl);
            status.put("analysisPath", analysisPath);
            status.put("fullUrl", baseUrl + analysisPath);
            status.put("username", username != null ? username : "未配置");
            status.put("isLoggedIn", sessionCookie != null);
            status.put("platform", "Hadoop数据分析平台");

            return objectMapper.writeValueAsString(status);

        } catch (Exception e) {
            logger.error("获取连接状态失败", e);
            return "获取连接状态失败: " + e.getMessage();
        }
    }
}

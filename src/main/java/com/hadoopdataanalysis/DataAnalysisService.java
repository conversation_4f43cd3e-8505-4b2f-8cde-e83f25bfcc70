package com.hadoopdataanalysis;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.cookie.BasicCookieStore;
import org.apache.hc.client5.http.cookie.CookieStore;
import org.apache.hc.core5.util.Timeout;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.client5.http.entity.UrlEncodedFormEntity;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.core5.http.Header;
import org.apache.hc.core5.http.NameValuePair;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.apache.hc.core5.http.message.BasicNameValuePair;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Hadoop数据分析服务类
 * 
 * 通过Web页面方式访问Hadoop数据分析平台
 * 提供表查询、结构获取、SQL执行等功能
 */
@Service
public class DataAnalysisService {

    private static final Logger logger = LoggerFactory.getLogger(DataAnalysisService.class);

    @Value("${hadoop.base-url:https://hadoop.greatld.com}")
    private String baseUrl;

    @Value("${hadoop.username:}")
    private String username;

    @Value("${hadoop.password:}")
    private String password;

    @Value("${hadoop.analysis-path:/dqp/dataAnalysis}")
    private String analysisPath;

    private final ObjectMapper objectMapper;
    private CloseableHttpClient httpClient;
    private CookieStore cookieStore;
    private String sessionCookie;

    public DataAnalysisService(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
        this.cookieStore = new BasicCookieStore();

        // 创建带有Cookie存储和浏览器模拟的HttpClient
        RequestConfig requestConfig = RequestConfig.custom()
            .setConnectTimeout(Timeout.ofMilliseconds(30000))
            .setResponseTimeout(Timeout.ofMilliseconds(60000))
            .build();

        this.httpClient = HttpClients.custom()
            .setDefaultCookieStore(cookieStore)
            .setDefaultRequestConfig(requestConfig)
            .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            .build();
    }

    /**
     * 登录到Hadoop数据分析平台
     *
     * @return 登录结果信息
     */
    @Tool(description = "登录到Hadoop数据分析平台，获取访问权限")
    public String login() {
        try {
            if (username == null || username.trim().isEmpty()) {
                return "错误：未配置用户名，请设置环境变量 HADOOP_USERNAME";
            }

            if (password == null || password.trim().isEmpty()) {
                return "错误：未配置密码，请设置环境变量 HADOOP_PASSWORD";
            }

            logger.info("开始登录到Hadoop数据分析平台: {}", baseUrl);

            // 步骤1: 访问登录页面，获取初始状态和Cookie
            Map<String, Object> pageInfo = loadLoginPage();
            if (pageInfo == null) {
                return "错误：无法访问登录页面";
            }

            // 步骤2: 尝试多种登录方式
            Map<String, Object> loginResult = attemptBrowserLogin();
            if (loginResult != null && "success".equals(loginResult.get("status"))) {
                return objectMapper.writeValueAsString(loginResult);
            }

            // 步骤3: 如果自动登录失败，提供详细的错误信息
            Map<String, Object> result = new HashMap<>();
            result.put("status", "login_failed");
            result.put("message", "自动登录失败，已尝试多种登录方式");
            result.put("loginUrl", baseUrl + "/login");
            result.put("username", username);
            result.put("baseUrl", baseUrl);
            result.put("suggestion", "请检查用户名和密码是否正确，或者网站登录机制是否有变化");

            return objectMapper.writeValueAsString(result);

        } catch (Exception e) {
            logger.error("登录失败", e);
            return "登录失败: " + e.getMessage();
        }
    }

    /**
     * 加载登录页面，获取初始状态
     */
    private Map<String, Object> loadLoginPage() {
        try {
            HttpGet loginPageRequest = new HttpGet(baseUrl + "/login");

            // 设置完整的浏览器请求头
            loginPageRequest.setHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8");
            loginPageRequest.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            loginPageRequest.setHeader("Accept-Encoding", "gzip, deflate, br");
            loginPageRequest.setHeader("Connection", "keep-alive");
            loginPageRequest.setHeader("Upgrade-Insecure-Requests", "1");
            loginPageRequest.setHeader("Sec-Fetch-Dest", "document");
            loginPageRequest.setHeader("Sec-Fetch-Mode", "navigate");
            loginPageRequest.setHeader("Sec-Fetch-Site", "none");

            try (CloseableHttpResponse response = httpClient.execute(loginPageRequest)) {
                String loginPageHtml = EntityUtils.toString(response.getEntity());

                Map<String, Object> pageInfo = new HashMap<>();
                pageInfo.put("html", loginPageHtml);
                pageInfo.put("statusCode", response.getCode());

                logger.info("成功加载登录页面，状态码: {}", response.getCode());
                return pageInfo;
            }

        } catch (Exception e) {
            logger.error("加载登录页面失败", e);
            return null;
        }
    }

    /**
     * 尝试浏览器模拟登录
     */
    private Map<String, Object> attemptBrowserLogin() {
        // 尝试多种登录方式
        String[] loginMethods = {
            "vue_api_login",
            "form_post_login",
            "ajax_login",
            "rest_api_login"
        };

        for (String method : loginMethods) {
            try {
                logger.info("尝试登录方式: {}", method);
                Map<String, Object> result = executeLoginMethod(method);
                if (result != null && "success".equals(result.get("status"))) {
                    logger.info("登录成功，使用方式: {}", method);
                    return result;
                }
            } catch (Exception e) {
                logger.debug("登录方式 {} 失败: {}", method, e.getMessage());
            }
        }

        return null;
    }

    /**
     * 执行具体的登录方法
     */
    private Map<String, Object> executeLoginMethod(String method) throws Exception {
        switch (method) {
            case "vue_api_login":
                return attemptVueApiLogin();
            case "form_post_login":
                return attemptFormPostLogin();
            case "ajax_login":
                return attemptAjaxLogin();
            case "rest_api_login":
                return attemptRestApiLogin();
            default:
                return null;
        }
    }

    /**
     * 尝试Vue.js API登录
     */
    private Map<String, Object> attemptVueApiLogin() throws Exception {
        // 根据浏览器分析得到的实际登录API端点
        String[] endpoints = {
            "/dmpapi/login",  // 实际的登录API
            "/api/auth/login",
            "/api/user/login",
            "/api/login",
            "/auth/login",
            "/user/login",
            "/login/submit",
            "/base/auth/login"
        };

        for (String endpoint : endpoints) {
            HttpPost loginRequest = new HttpPost(baseUrl + endpoint);

            // 设置Vue.js应用常用的请求头
            loginRequest.setHeader("Content-Type", "application/json;charset=UTF-8");
            loginRequest.setHeader("Accept", "application/json, text/plain, */*");
            loginRequest.setHeader("X-Requested-With", "XMLHttpRequest");
            loginRequest.setHeader("Referer", baseUrl + "/login");
            loginRequest.setHeader("Origin", baseUrl);

            // 准备登录数据
            Map<String, Object> loginData = new HashMap<>();
            loginData.put("username", username);
            loginData.put("password", password);
            loginData.put("loginType", "password");

            String jsonData = objectMapper.writeValueAsString(loginData);
            loginRequest.setEntity(new StringEntity(jsonData, ContentType.APPLICATION_JSON));

            try (CloseableHttpResponse response = httpClient.execute(loginRequest)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                int statusCode = response.getCode();

                logger.debug("尝试端点 {}, 状态码: {}, 响应: {}", endpoint, statusCode, responseBody);

                if (statusCode == 200 && responseBody != null) {
                    try {
                        JsonNode responseJson = objectMapper.readTree(responseBody);

                        // 检查多种成功标识
                        boolean isSuccess = false;
                        if (responseJson.has("success") && responseJson.get("success").asBoolean()) {
                            isSuccess = true;
                        } else if (responseJson.has("code") && responseJson.get("code").asInt() == 200) {
                            isSuccess = true;
                        } else if (responseJson.has("status") && "success".equals(responseJson.get("status").asText())) {
                            isSuccess = true;
                        }

                        if (isSuccess) {
                            // 保存会话信息
                            Header[] cookies = response.getHeaders("Set-Cookie");
                            if (cookies.length > 0) {
                                sessionCookie = cookies[0].getValue();
                            }

                            Map<String, Object> result = new HashMap<>();
                            result.put("status", "success");
                            result.put("message", "Vue.js API登录成功");
                            result.put("username", username);
                            result.put("baseUrl", baseUrl);
                            result.put("endpoint", endpoint);
                            result.put("method", "vue_api_login");

                            return result;
                        }
                    } catch (Exception e) {
                        // 响应不是JSON，继续尝试
                    }
                }
            }
        }

        return null;
    }

    /**
     * 尝试表单POST登录
     */
    private Map<String, Object> attemptFormPostLogin() throws Exception {
        HttpPost loginRequest = new HttpPost(baseUrl + "/login");

        // 设置表单提交请求头
        loginRequest.setHeader("Content-Type", "application/x-www-form-urlencoded");
        loginRequest.setHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
        loginRequest.setHeader("Referer", baseUrl + "/login");
        loginRequest.setHeader("Origin", baseUrl);

        // 准备表单数据
        List<NameValuePair> formParams = new ArrayList<>();
        formParams.add(new BasicNameValuePair("username", username));
        formParams.add(new BasicNameValuePair("password", password));

        loginRequest.setEntity(new UrlEncodedFormEntity(formParams));

        try (CloseableHttpResponse response = httpClient.execute(loginRequest)) {
            int statusCode = response.getCode();
            String responseBody = EntityUtils.toString(response.getEntity());

            // 检查重定向或成功响应
            if (statusCode == 302 || statusCode == 301) {
                Header locationHeader = response.getFirstHeader("Location");
                if (locationHeader != null && !locationHeader.getValue().contains("login")) {
                    Map<String, Object> result = new HashMap<>();
                    result.put("status", "success");
                    result.put("message", "表单POST登录成功");
                    result.put("username", username);
                    result.put("baseUrl", baseUrl);
                    result.put("method", "form_post_login");
                    result.put("redirectUrl", locationHeader.getValue());

                    return result;
                }
            }
        }

        return null;
    }

    /**
     * 尝试AJAX登录
     */
    private Map<String, Object> attemptAjaxLogin() throws Exception {
        HttpPost loginRequest = new HttpPost(baseUrl + "/login");

        // 设置AJAX请求头
        loginRequest.setHeader("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        loginRequest.setHeader("Accept", "*/*");
        loginRequest.setHeader("X-Requested-With", "XMLHttpRequest");
        loginRequest.setHeader("Referer", baseUrl + "/login");
        loginRequest.setHeader("Origin", baseUrl);

        // 准备表单数据
        List<NameValuePair> formParams = new ArrayList<>();
        formParams.add(new BasicNameValuePair("username", username));
        formParams.add(new BasicNameValuePair("password", password));

        loginRequest.setEntity(new UrlEncodedFormEntity(formParams));

        try (CloseableHttpResponse response = httpClient.execute(loginRequest)) {
            String responseBody = EntityUtils.toString(response.getEntity());
            int statusCode = response.getCode();

            if (statusCode == 200 && responseBody != null) {
                // 检查成功标识
                if (responseBody.contains("success") || responseBody.contains("\"code\":200") ||
                    responseBody.contains("登录成功")) {

                    Map<String, Object> result = new HashMap<>();
                    result.put("status", "success");
                    result.put("message", "AJAX登录成功");
                    result.put("username", username);
                    result.put("baseUrl", baseUrl);
                    result.put("method", "ajax_login");

                    return result;
                }
            }
        }

        return null;
    }

    /**
     * 尝试REST API登录
     */
    private Map<String, Object> attemptRestApiLogin() throws Exception {
        String[] endpoints = {"/api/v1/login", "/rest/login", "/service/login"};

        for (String endpoint : endpoints) {
            HttpPost loginRequest = new HttpPost(baseUrl + endpoint);

            loginRequest.setHeader("Content-Type", "application/json");
            loginRequest.setHeader("Accept", "application/json");

            Map<String, String> loginData = new HashMap<>();
            loginData.put("username", username);
            loginData.put("password", password);

            String jsonData = objectMapper.writeValueAsString(loginData);
            loginRequest.setEntity(new StringEntity(jsonData));

            try (CloseableHttpResponse response = httpClient.execute(loginRequest)) {
                if (response.getCode() == 200) {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    if (responseBody.contains("success") || responseBody.contains("token")) {
                        Map<String, Object> result = new HashMap<>();
                        result.put("status", "success");
                        result.put("message", "REST API登录成功");
                        result.put("username", username);
                        result.put("baseUrl", baseUrl);
                        result.put("method", "rest_api_login");
                        result.put("endpoint", endpoint);

                        return result;
                    }
                }
            }
        }

        return null;
    }

    /**
     * 获取数据库列表
     * 
     * @return 数据库列表的JSON字符串
     */
    @Tool(description = "获取Hadoop数据分析平台中的所有数据库列表")
    public String getDatabaseList() {
        try {
            // 确保已登录
            if (sessionCookie == null) {
                String loginResult = login();
                if (!loginResult.contains("success")) {
                    return loginResult;
                }
            }
            
            // 访问数据分析页面
            HttpGet request = new HttpGet(baseUrl + analysisPath);
            if (sessionCookie != null) {
                request.setHeader("Cookie", sessionCookie);
            }
            
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                String html = EntityUtils.toString(response.getEntity());
                Document doc = Jsoup.parse(html);
                
                // 解析数据库列表
                List<Map<String, Object>> databases = new ArrayList<>();
                
                // 查找数据库树结构
                Elements dbElements = doc.select(".database-tree .database-item, .tree-node[data-type=database]");
                
                for (Element dbElement : dbElements) {
                    Map<String, Object> database = new HashMap<>();
                    
                    String dbName = dbElement.text();
                    if (dbName.contains(".")) {
                        dbName = dbName.substring(0, dbName.indexOf("."));
                    }
                    
                    database.put("databaseName", dbName);
                    database.put("type", "database");
                    
                    // 尝试获取表数量
                    Elements tableElements = dbElement.parent().select(".table-item");
                    database.put("tableCount", tableElements.size());
                    
                    databases.add(database);
                }
                
                // 如果没有找到特定的数据库元素，尝试从页面脚本中解析
                if (databases.isEmpty()) {
                    Elements scripts = doc.select("script");
                    for (Element script : scripts) {
                        String scriptContent = script.html();
                        if (scriptContent.contains("applydata") || scriptContent.contains("database")) {
                            // 尝试解析JavaScript中的数据库信息
                            if (scriptContent.contains("applydata")) {
                                databases.add(createDatabaseInfo("applydata"));
                            }
                            if (scriptContent.contains("applydata_bi")) {
                                databases.add(createDatabaseInfo("applydata_bi"));
                            }
                            if (scriptContent.contains("applydata_bi_ads")) {
                                databases.add(createDatabaseInfo("applydata_bi_ads"));
                            }
                            if (scriptContent.contains("applydata_bi_dw")) {
                                databases.add(createDatabaseInfo("applydata_bi_dw"));
                            }
                            break;
                        }
                    }
                }
                
                Map<String, Object> result = new HashMap<>();
                result.put("total", databases.size());
                result.put("databases", databases);
                result.put("source", "hadoop-data-analysis-platform");
                
                return objectMapper.writeValueAsString(result);
            }
            
        } catch (Exception e) {
            logger.error("获取数据库列表失败", e);
            return "获取数据库列表失败: " + e.getMessage();
        }
    }

    /**
     * 搜索数据库表
     * 
     * @param tableNamePattern 表名模式，支持库名.表名格式
     * @param schemaName 数据库名称
     * @param limit 返回结果数量限制
     * @return 表列表的JSON字符串
     */
    @Tool(description = "搜索Hadoop数据分析平台中的表，支持库名.表名格式搜索")
    public String searchTables(String tableNamePattern, String schemaName, int limit) {
        try {
            // 确保已登录
            if (sessionCookie == null) {
                String loginResult = login();
                if (!loginResult.contains("success")) {
                    return loginResult;
                }
            }
            
            if (limit <= 0) limit = 50;
            if (limit > 200) limit = 200;
            
            // 处理库名.表名格式
            String actualSchema = schemaName;
            String actualTablePattern = tableNamePattern;
            
            if (tableNamePattern != null && tableNamePattern.contains(".")) {
                String[] parts = tableNamePattern.split("\\.", 2);
                actualSchema = parts[0];
                actualTablePattern = parts[1];
            }
            
            // 访问数据分析页面
            HttpGet request = new HttpGet(baseUrl + analysisPath);
            if (sessionCookie != null) {
                request.setHeader("Cookie", sessionCookie);
            }
            
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                String html = EntityUtils.toString(response.getEntity());
                Document doc = Jsoup.parse(html);
                
                List<Map<String, Object>> tables = new ArrayList<>();
                
                // 查找表列表
                Elements tableElements = doc.select(".table-item, .tree-node[data-type=table]");
                
                int count = 0;
                for (Element tableElement : tableElements) {
                    if (count >= limit) break;
                    
                    String tableName = tableElement.text();
                    String fullTableName = tableName;
                    
                    // 如果表名不包含库名，添加库名
                    if (!tableName.contains(".") && actualSchema != null) {
                        fullTableName = actualSchema + "." + tableName;
                    }
                    
                    // 检查是否匹配搜索模式
                    if (actualTablePattern != null && !actualTablePattern.isEmpty()) {
                        if (!tableName.toLowerCase().contains(actualTablePattern.toLowerCase())) {
                            continue;
                        }
                    }
                    
                    Map<String, Object> table = new HashMap<>();
                    table.put("tableName", tableName);
                    table.put("fullName", fullTableName);
                    table.put("schema", actualSchema);
                    table.put("tableType", "TABLE");
                    
                    tables.add(table);
                    count++;
                }
                
                Map<String, Object> result = new HashMap<>();
                result.put("total", tables.size());
                result.put("tables", tables);
                result.put("searchPattern", tableNamePattern);
                result.put("schema", actualSchema);
                result.put("limit", limit);
                
                return objectMapper.writeValueAsString(result);
            }
            
        } catch (Exception e) {
            logger.error("搜索表失败", e);
            return "搜索表失败: " + e.getMessage();
        }
    }

    /**
     * 创建数据库信息对象
     */
    private Map<String, Object> createDatabaseInfo(String dbName) {
        Map<String, Object> database = new HashMap<>();
        database.put("databaseName", dbName);
        database.put("type", "database");
        database.put("tableCount", 0);
        return database;
    }

    /**
     * 获取表结构信息
     *
     * @param tableName 表名，支持库名.表名格式
     * @return 表结构信息的JSON字符串
     */
    @Tool(description = "获取指定表的结构信息，包括字段名称和类型")
    public String getTableStructure(String tableName) {
        try {
            // 确保已登录
            if (sessionCookie == null) {
                String loginResult = login();
                if (!loginResult.contains("success")) {
                    return loginResult;
                }
            }

            // 构造DESCRIBE查询
            String describeSQL = "DESCRIBE " + tableName;

            Map<String, Object> result = new HashMap<>();
            result.put("tableName", tableName);
            result.put("sql", describeSQL);
            result.put("message", "请使用executeSQL工具执行以下SQL查询来获取表结构");
            result.put("suggestedSQL", describeSQL);

            return objectMapper.writeValueAsString(result);

        } catch (Exception e) {
            logger.error("获取表结构失败", e);
            return "获取表结构失败: " + e.getMessage();
        }
    }

    /**
     * 生成快捷SQL语句
     *
     * @param tableName 表名
     * @param sqlType SQL类型：select、count、describe
     * @return 生成的SQL语句
     */
    @Tool(description = "为指定表生成快捷SQL语句，支持select、count、describe类型")
    public String generateQuickSQL(String tableName, String sqlType) {
        try {
            if (tableName == null || tableName.trim().isEmpty()) {
                return "表名不能为空";
            }

            String actualTableName = tableName.trim();

            String sql;
            switch (sqlType.toLowerCase()) {
                case "select":
                    sql = "SELECT * FROM " + actualTableName + " LIMIT 101";
                    break;
                case "count":
                    sql = "SELECT COUNT(1) FROM " + actualTableName;
                    break;
                case "describe":
                case "desc":
                    sql = "DESCRIBE " + actualTableName;
                    break;
                default:
                    return "不支持的SQL类型，支持的类型：select、count、describe";
            }

            Map<String, Object> result = new HashMap<>();
            result.put("tableName", actualTableName);
            result.put("sqlType", sqlType);
            result.put("sql", sql);
            result.put("message", "已生成SQL语句，请使用executeSQL工具执行");

            return objectMapper.writeValueAsString(result);

        } catch (Exception e) {
            logger.error("生成快捷SQL失败", e);
            return "生成快捷SQL失败: " + e.getMessage();
        }
    }

    /**
     * 执行SQL查询（模拟）
     *
     * @param sql SQL查询语句
     * @param maxRows 最大返回行数
     * @return 查询结果说明
     */
    @Tool(description = "模拟SQL查询执行，返回执行说明（实际执行需要在Web页面中进行）")
    public String executeSQL(String sql, int maxRows) {
        try {
            if (maxRows <= 0) maxRows = 101;
            if (maxRows > 10000) maxRows = 10000;

            // 安全检查
            String trimmedSql = sql.trim().toUpperCase();
            if (!trimmedSql.startsWith("SELECT") && !trimmedSql.startsWith("DESCRIBE") && !trimmedSql.startsWith("SHOW")) {
                return "安全限制：只允许执行SELECT、DESCRIBE、SHOW查询语句";
            }

            Map<String, Object> result = new HashMap<>();
            result.put("sql", sql);
            result.put("maxRows", maxRows);
            result.put("status", "prepared");
            result.put("message", "SQL语句已准备就绪");
            result.put("instructions", "请在Hadoop数据分析平台页面 (" + baseUrl + analysisPath + ") 中执行此SQL语句");
            result.put("webUrl", baseUrl + analysisPath);

            return objectMapper.writeValueAsString(result);

        } catch (Exception e) {
            logger.error("SQL执行准备失败", e);
            return "SQL执行准备失败: " + e.getMessage();
        }
    }

    /**
     * 获取平台连接状态
     *
     * @return 连接状态信息
     */
    @Tool(description = "获取Hadoop数据分析平台的连接状态和基本信息")
    public String getConnectionStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("baseUrl", baseUrl);
            status.put("analysisPath", analysisPath);
            status.put("fullUrl", baseUrl + analysisPath);
            status.put("username", username != null ? username : "未配置");
            status.put("isLoggedIn", sessionCookie != null);
            status.put("platform", "Hadoop数据分析平台");

            return objectMapper.writeValueAsString(status);

        } catch (Exception e) {
            logger.error("获取连接状态失败", e);
            return "获取连接状态失败: " + e.getMessage();
        }
    }
}

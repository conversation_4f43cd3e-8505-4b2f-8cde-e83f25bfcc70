package com.hadoopdataanalysis;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * 登录功能测试类
 */
@SpringBootTest
@TestPropertySource(properties = {
    "hadoop.username=wangjp",
    "hadoop.password=qqqq1111",
    "hadoop.base-url=https://hadoop.greatld.com",
    "logging.level.com.hadoopdataanalysis=DEBUG"
})
public class LoginTest {

    @Autowired
    private DataAnalysisService dataAnalysisService;

    @Test
    public void testLogin() {
        try {
            // 执行登录测试
            String result = dataAnalysisService.login();

            System.out.println("=== 登录测试结果 ===");
            System.out.println(result);
            System.out.println("==================");

            // 验证结果
            if (result.contains("success") || result.contains("manual_required")) {
                System.out.println("✓ 登录功能正常工作");
            } else {
                System.out.println("✗ 登录功能存在问题");
            }

        } catch (Exception e) {
            System.err.println("登录测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testGetConnectionStatus() {
        try {
            // 测试连接状态
            String result = dataAnalysisService.getConnectionStatus();

            System.out.println("=== 连接状态测试结果 ===");
            System.out.println(result);
            System.out.println("=====================");

        } catch (Exception e) {
            System.err.println("连接状态测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
